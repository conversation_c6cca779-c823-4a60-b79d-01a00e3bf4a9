[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "youtube-transcript-api"
version = "1.1.0"
description = "This is an python API which allows you to get the transcripts/subtitles for a given YouTube video. It also works for automatically generated subtitles, supports translating subtitles and it does not require a headless browser, like other selenium based solutions do!"
readme = "README.md"
license = "MIT"
authors = [
    "Jonas Depoix <<EMAIL>>",
]
homepage = "https://github.com/jdepoix/youtube-transcript-api"
repository = "https://github.com/jdepoix/youtube-transcript-api"
keywords = [
    "cli",
    "subtitle",
    "subtitles",
    "transcript",
    "transcripts",
    "youtube",
    "youtube-api",
    "youtube-subtitles",
    "youtube-transcripts",
]
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

[tool.poetry.scripts]
youtube_transcript_api = "youtube_transcript_api.__main__:main"

[tool.poe.tasks]
test = "pytest youtube_transcript_api"
ci-test.shell = "coverage run -m pytest youtube_transcript_api && coverage xml"
coverage.shell = "coverage run -m pytest youtube_transcript_api && coverage report -m --fail-under=100"
format = "ruff format youtube_transcript_api"
ci-format = "ruff format youtube_transcript_api --check"
lint = "ruff check youtube_transcript_api"
precommit.shell = "poe format && poe lint && poe coverage"

[tool.poetry.dependencies]
python = ">=3.8,<3.14"
requests = "*"
defusedxml = "^0.7.1"
gradio = "*"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "^8.3.3"
coverage = "^7.6.1"
httpretty = "<1.1"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
ruff = "^0.6.8"

[tool.coverage.run]
source = ["youtube_transcript_api"]

[tool.coverage.report]
omit = ["*/__main__.py", "youtube_transcript_api/test/*"]
exclude_lines = [
    "pragma: no cover",

    # Don't complain about missing debug-only code:
    "def __unicode__",
    "def __repr__",
    "if self\\.debug",

    # Don't complain if tests don't hit defensive assertion code:
    "raise AssertionError",
    "raise NotImplementedError",

    # Don't complain if non-runnable code isn't run:
    "if 0:",
    "if __name__ == .__main__.:",

    # Don't complain about empty stubs of abstract methods
    "@abstractmethod",
    "@abstractclassmethod",
    "@abstractstaticmethod"
]
show_missing = true