---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

DO NOT DELETE THIS! Please take the time to fill this out properly. I am not able to help you if I do not know what you are executing and what error messages you are getting. If you are having problems with a specific video make sure to **include the video id**.

# To Reproduce
Steps to reproduce the behavior:

### What code / cli command are you executing?
For example: I am running
```
YouTubeTranscriptApi.get_transcript ...
```

### Which Python version are you using?
Python x.y

### Which version of youtube-transcript-api are you using?
youtube-transcript-api x.y.z


# Expected behavior
Describe what you expected to happen. 

For example: I expected to receive the english transcript

# Actual behaviour
Describe what is happening instead of the **Expected behavior**. Add **error messages** if there are any. 

For example: Instead I received the following error message:
```
# ... error message ...
```
