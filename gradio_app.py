import gradio as gr
from youtube_transcript_api import YouTubeTranscriptApi, NoTranscriptFound, TranscriptsDisabled
import re

def get_video_id(youtube_url):
    """Extracts video ID from YouTube URL."""
    patterns = [
        r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([^&\s]+)',
        r'(?:https?:\/\/)?youtu\.be\/([^?\s]+)',
        r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([^"?\s]+)',
        r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([^"?\s]+)'
    ]
    for pattern in patterns:
        match = re.search(pattern, youtube_url)
        if match:
            return match.group(1)
    return None

def format_transcript(transcript_pieces):
    """Formats transcript pieces into a single string."""
    return "\n".join([piece.text for piece in transcript_pieces])

def get_transcripts(youtube_url, target_language=None):
    video_id = get_video_id(youtube_url)
    if not video_id:
        return "Неверный URL YouTube", ""

    original_transcript_text = ""
    translated_transcript_text = ""

    try:
        # STAGE 1: Listing transcripts
        try:
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        except Exception as e:
            return f"Ошибка на этапе STAGE 1 (list_transcripts): {type(e).__name__}: {str(e)}", ""

        # STAGE 2: Finding original transcript
        original_lang_transcript = None
        preferred_langs = ['en', 'ru', 'de', 'fr', 'es']
        try:
            for lang in preferred_langs:
                try:
                    original_lang_transcript = transcript_list.find_transcript([lang])
                    break
                except NoTranscriptFound:
                    continue
            
            if not original_lang_transcript and list(transcript_list):
                original_lang_transcript = list(transcript_list)[0]
        
        except IndexError:
             return "Ошибка на этапе STAGE 2 (find_transcript/IndexError): Транскрипция не найдена.", ""
        except NoTranscriptFound:
            return "Ошибка на этапе STAGE 2 (find_transcript/NoTranscriptFound): Транскрипция не найдена.", ""
        except Exception as e:
            return f"Ошибка на этапе STAGE 2 (find_transcript): {type(e).__name__}: {str(e)}", ""
        
        if not original_lang_transcript:
             return "Транскрипция не найдена для этого видео (не удалось выбрать оригинальную).", ""

        # STAGE 3: Fetching original transcript pieces
        try:
            original_transcript_pieces = original_lang_transcript.fetch()
        except Exception as e:
            return f"Ошибка на этапе STAGE 3 (fetch original): {type(e).__name__}: {str(e)}", ""

        # STAGE 4: Formatting original transcript
        try:
            original_transcript_text = format_transcript(original_transcript_pieces)
        except Exception as e:
            return f"Ошибка на этапе STAGE 4 (format original): {type(e).__name__}: {str(e)}", ""

        # STAGE 5: Translation (if requested)
        if target_language and target_language.strip():
            if original_lang_transcript.is_translatable:
                try:
                    # STAGE 5a: Fetching translated pieces
                    translated_transcript_object = original_lang_transcript.translate(target_language)
                    translated_transcript_pieces = translated_transcript_object.fetch()
                except Exception as e:
                    translated_transcript_text = f"Ошибка на этапе STAGE 5a (fetch translated): {type(e).__name__}: {str(e)}"
                else:
                    # STAGE 5b: Formatting translated transcript
                    try:
                        translated_transcript_text = format_transcript(translated_transcript_pieces)
                    except Exception as e:
                        translated_transcript_text = f"Ошибка на этапе STAGE 5b (format translated): {type(e).__name__}: {str(e)}"
            else:
                translated_transcript_text = "Оригинальная транскрипция непереводима."
        
        return original_transcript_text, translated_transcript_text

    # Specific library exceptions that might not be caught by stage-specific handlers above
    except TranscriptsDisabled:
        return "Транскрипции отключены для этого видео (общая ошибка TranscriptsDisabled).", ""
    except NoTranscriptFound:
        return "Транскрипция не найдена для этого видео (общая ошибка NoTranscriptFound).", ""
    # Catch-all for any other unexpected errors not caught by stage-specific handlers
    except Exception as e:
        return f"Непредвиденная общая ошибка: {type(e).__name__}: {str(e)}", ""

with gr.Blocks(theme=gr.themes.Soft(), css="""
html, body, gradio-app { /* NEW: Ensure full viewport height foundation */
    height: 100%;
    margin: 0;
}
.gradio-container { /* UPDATED: Make it a tall flex container */
    height: 100%;
    max-width: 100% !important;
    padding: 20px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box; /* Important for padding + height 100% */
}
#main_content_row { /* NEW: Make the main row stretch and align columns */
    display: flex;
    flex-grow: 1; /* Takes available vertical space in .gradio-container */
    align-items: stretch; /* Makes columns (input and output) same height */
    min-height: 0; /* Important for nested flex elements */
}
    .textbox_container {
        height: auto !important;
    }
    .scroll-hide {
        overflow-y: auto !important;
        max-height: 600px !important;
    }
    #output_flex_column {
        display: flex;
        flex-direction: column;
    }
    #output_flex_column > .fill-height-textbox { /* Обертка компонента Textbox */
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        min-height: 0; 
    }
    /* НОВОЕ ПРАВИЛО для внутреннего контейнера Gradio */
    #output_flex_column > .fill-height-textbox .input-container {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }
    #output_flex_column > .fill-height-textbox textarea {
        flex-grow: 1; 
        height: 100% !important; /* Для переопределения встроенных стилей Gradio */
        /* Класс .scroll-hide (с max-height: 600px) также будет влиять */
    }
""") as iface:
    gr.Markdown("## Получение транскрипции и перевода видео YouTube")
    gr.Markdown("Введите URL видео YouTube и, если нужно, код языка для перевода (например, 'en' для английского, 'es' для испанского).")
    
    with gr.Row(elem_id="main_content_row"):
        with gr.Column(scale=1, min_width=300):
            youtube_url_input = gr.Textbox(
                label="URL видео YouTube", 
                placeholder="Например: https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            )
            target_language_input = gr.Textbox(
                label="Код языка для перевода (например, en, es, de) - оставьте пустым, если перевод не нужен"
            )
            submit_button = gr.Button("Получить транскрипцию")

        with gr.Column(scale=2, elem_id="output_flex_column"):
            original_transcript_output = gr.Textbox(
                label="Оригинальная транскрипция",
                interactive=False,
                show_copy_button=True,
                autoscroll=False,
                elem_classes="fill-height-textbox scroll-hide"
            )
            translated_transcript_output = gr.Textbox(
                label="Переведенная транскрипция",
                interactive=False,
                show_copy_button=True,
                autoscroll=False,
                elem_classes="fill-height-textbox scroll-hide"
            )
                
    submit_button.click(
        fn=get_transcripts,
        inputs=[youtube_url_input, target_language_input],
        outputs=[original_transcript_output, translated_transcript_output],
        api_name="get_transcript_api"
    )

if __name__ == '__main__':
    iface.launch()
