from youtube_transcript_api import YouTubeTranscriptApi

# Создаем экземпляр API
ytt_api = YouTubeTranscriptApi()

# Получаем субтитры для видео (используйте ID видео, а не полный URL)
# Например, для https://www.youtube.com/watch?v=CmAGOcbU1T4 ID будет 12345
transcript = ytt_api.fetch('CmAGOcbU1T4')

# Перебираем все фрагменты субтитров
for snippet in transcript:
    print(f"{snippet.start}: {snippet.text}")